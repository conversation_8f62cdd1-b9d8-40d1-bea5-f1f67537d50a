#include "BiomeDataEditor.h"
#include "EditorStyleSet.h"
#include "Widgets/Docking/SDockTab.h"
#include "PropertyEditorModule.h"
#include "IDetailsView.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "DetailWidgetRow.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SComboBox.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SSplitter.h"
#include "Toolkits/ToolkitManager.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Engine/Texture2D.h"
#include "Engine/Texture2DDynamic.h"
#include "LandscapeStreamingProxy.h"
#include "LandscapeComponent.h"
#include "LandscapeDataAccess.h"
#include "EngineUtils.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Misc/MessageDialog.h"
#include "Slate/SlateTextures.h"

const FName FBiomeDataEditor::BiomeDataEditorAppIdentifier = FName("BiomeDataEditor");
const FName FBiomeDataEditor::DetailsTabId = FName("BiomeDataEditor_Details");
const FName FBiomeDataEditor::ViabilityPreviewTabId = FName("BiomeDataEditor_ViabilityPreview");

FBiomeDataEditor::FBiomeDataEditor()
{
}

FBiomeDataEditor::~FBiomeDataEditor()
{
}

void FBiomeDataEditor::InitBiomeDataEditor(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UBiomeData* InBiomeData)
{
    BiomeDataAsset = InBiomeData;

    // Create details view with custom details
    FPropertyEditorModule& PropertyEditorModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");

    FDetailsViewArgs DetailsViewArgs;
    DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
    DetailsViewArgs.bAllowSearch = true;
    DetailsViewArgs.bShowOptions = true;
    DetailsViewArgs.bShowModifiedPropertiesOption = true;

    DetailsView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);
    DetailsView->SetObject(BiomeDataAsset);

    // Register custom details
    DetailsView->RegisterInstancedCustomPropertyLayout(
        UBiomeData::StaticClass(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FBiomeDataDetailsCustomization::MakeInstance)
    );

    // Create preview widget
    ViabilityPreviewWidget = SNew(SBiomeViabilityPreviewWidget, this);

    // Initialize preview textures
    CreatePreviewTextures();

    // Layout
    const TSharedRef<FTabManager::FLayout> StandaloneDefaultLayout = FTabManager::NewLayout("BiomeDataEditor_Layout_v1")
        ->AddArea
        (
            FTabManager::NewPrimaryArea()
            ->SetOrientation(Orient_Horizontal)
            ->Split
            (
                FTabManager::NewStack()
                ->SetSizeCoefficient(0.6f)
                ->AddTab(DetailsTabId, ETabState::OpenedTab)
            )
            ->Split
            (
                FTabManager::NewStack()
                ->SetSizeCoefficient(0.4f)
                ->AddTab(ViabilityPreviewTabId, ETabState::OpenedTab)
            )
        );

    InitAssetEditor(Mode, InitToolkitHost, BiomeDataEditorAppIdentifier, StandaloneDefaultLayout, true, true, InBiomeData);
}

TSharedRef<SDockTab> FBiomeDataEditor::SpawnDetailsTab(const FSpawnTabArgs& Args)
{
    return SNew(SDockTab)
        .Label(FText::FromString("Details"))
        [
            DetailsView.ToSharedRef()
        ];
}

TSharedRef<SDockTab> FBiomeDataEditor::SpawnViabilityPreviewTab(const FSpawnTabArgs& Args)
{
    return SNew(SDockTab)
        .Label(FText::FromString("Viability Preview"))
        [
            ViabilityPreviewWidget.ToSharedRef()
        ];
}

void FBiomeDataEditor::RegisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::RegisterTabSpawners(InTabManager);

    InTabManager->RegisterTabSpawner(DetailsTabId, FOnSpawnTab::CreateSP(this, &FBiomeDataEditor::SpawnDetailsTab))
        .SetDisplayName(FText::FromString("Details"));

    InTabManager->RegisterTabSpawner(ViabilityPreviewTabId, FOnSpawnTab::CreateSP(this, &FBiomeDataEditor::SpawnViabilityPreviewTab))
        .SetDisplayName(FText::FromString("Viability Preview"));
}

void FBiomeDataEditor::UnregisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::UnregisterTabSpawners(InTabManager);

    InTabManager->UnregisterTabSpawner(DetailsTabId);
    InTabManager->UnregisterTabSpawner(ViabilityPreviewTabId);
}

void FBiomeDataEditor::CreatePreviewTextures()
{
    int32 TextureSize = 512;

    // Helper lambda to create texture
    auto CreateTexture = [TextureSize](const FString& Name) -> UTexture2D*
        {
            UTexture2D* Texture = UTexture2D::CreateTransient(TextureSize, TextureSize, PF_B8G8R8A8, FName(*Name));
            Texture->CompressionSettings = TC_Default;
            Texture->SRGB = true;
            Texture->AddToRoot(); // Prevent GC
            Texture->UpdateResource();
            return Texture;
        };

    // Create main viability preview
    ViabilityPreviewTexture = CreateTexture("ViabilityPreview");

    // Create terrain attribute textures
    TArray<FString> AttributeNames = {
        TEXT("Height"), TEXT("Slope"), TEXT("Aspect"), TEXT("Curvature"),
        TEXT("Occlusion"), TEXT("FlowAccumulation"), TEXT("WindExposure")
    };

    for (const FString& AttrName : AttributeNames)
    {
        UTexture2D* AttrTexture = CreateTexture(FString::Printf(TEXT("ViabilityPreview_%s"), *AttrName));
        TerrainAttributeTextures.Add(AttrName, AttrTexture);
    }
}

void FBiomeDataEditor::BakeTerrainData()
{
    if (!SelectedLandscapeProxy.IsValid())
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::FromString("Please select a Landscape Streaming Proxy first!"));
        return;
    }

    ALandscapeStreamingProxy* Landscape = SelectedLandscapeProxy.Get();

    // Get landscape bounds
    Landscape->GetActorBounds(false, CachedLandscapeOrigin, CachedLandscapeExtent);

    int32 TextureSize = 512;

    // Show progress notification
    FNotificationInfo Info(FText::FromString("Baking terrain data..."));
    Info.bFireAndForget = false;
    Info.ExpireDuration = 2.0f;

    auto NotificationItem = FSlateNotificationManager::Get().AddNotification(Info);
    NotificationItem->SetCompletionState(SNotificationItem::CS_Pending);

    // Calculate terrain attributes for each texture
    for (auto& Pair : TerrainAttributeTextures)
    {
        const FString& AttributeName = Pair.Key;
        UTexture2D* Texture = Pair.Value;

        // Get mip 0
        FTexture2DMipMap* Mip0 = &Texture->GetPlatformData()->Mips[0];

        // Lock texture data
        FColor* TextureData = (FColor*)Mip0->BulkData.Lock(LOCK_READ_WRITE);

        // Sample landscape and calculate attribute
        for (int32 Y = 0; Y < TextureSize; Y++)
        {
            for (int32 X = 0; X < TextureSize; X++)
            {
                // Convert texture coords to world position
                float U = (float)X / (TextureSize - 1);
                float V = (float)Y / (TextureSize - 1);

                FVector WorldPos = CachedLandscapeOrigin + FVector(
                    FMath::Lerp(-CachedLandscapeExtent.X, CachedLandscapeExtent.X, U),
                    FMath::Lerp(-CachedLandscapeExtent.Y, CachedLandscapeExtent.Y, V),
                    0
                );

                // Calculate attribute value
                float Value = 0.0f;
                if (AttributeName == TEXT("Height"))
                {
                    Value = CalculateHeightAtLocation(WorldPos, Landscape);
                }
                else if (AttributeName == TEXT("Slope"))
                {
                    Value = CalculateSlopeAtLocation(WorldPos, Landscape);
                }
                else if (AttributeName == TEXT("Aspect"))
                {
                    Value = CalculateAspectAtLocation(WorldPos, Landscape);
                }
                else if (AttributeName == TEXT("Curvature"))
                {
                    Value = CalculateCurvatureAtLocation(WorldPos, Landscape);
                }
                else if (AttributeName == TEXT("Occlusion"))
                {
                    Value = CalculateOcclusionAtLocation(WorldPos, Landscape);
                }
                else if (AttributeName == TEXT("FlowAccumulation"))
                {
                    Value = CalculateFlowAccumulationAtLocation(WorldPos, Landscape);
                }
                else if (AttributeName == TEXT("WindExposure"))
                {
                    Value = CalculateWindExposureAtLocation(WorldPos, Landscape, FVector(1, 0, 0));
                }

                // Write to texture
                uint8 ByteValue = FMath::Clamp(FMath::RoundToInt(Value * 255.0f), 0, 255);
                int32 Index = Y * TextureSize + X;
                TextureData[Index] = FColor(ByteValue, ByteValue, ByteValue, 255);
            }
        }

        // Unlock and update
        Mip0->BulkData.Unlock();
        Texture->UpdateResource();
    }

    NotificationItem->SetText(FText::FromString("Terrain data baked successfully!"));
    NotificationItem->SetCompletionState(SNotificationItem::CS_Success);
    NotificationItem->ExpireAndFadeout();
}

void FBiomeDataEditor::PreviewSpeciesViability(int32 SpeciesIndex)
{
    if (!BiomeDataAsset || SpeciesIndex >= BiomeDataAsset->Species.Num())
    {
        return;
    }

    if (TerrainAttributeTextures.Num() == 0)
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::FromString("Please bake terrain data first!"));
        return;
    }

    const FBiomeSpecies& Species = BiomeDataAsset->Species[SpeciesIndex];
    int32 TextureSize = 512;

    // Get mip 0
    FTexture2DMipMap* Mip0 = &ViabilityPreviewTexture->GetPlatformData()->Mips[0];
    FColor* ViabilityData = (FColor*)Mip0->BulkData.Lock(LOCK_READ_WRITE);

    // Read all terrain attribute data
    TMap<FString, TArray<float>> AttributeData;
    for (const auto& Pair : TerrainAttributeTextures)
    {
        const FString& AttrName = Pair.Key;
        UTexture2D* AttrTexture = Pair.Value;

        TArray<float>& Data = AttributeData.Add(AttrName);
        Data.SetNum(TextureSize * TextureSize);

        FTexture2DMipMap* AttrMip = &AttrTexture->GetPlatformData()->Mips[0];
        FColor* AttrData = (FColor*)AttrMip->BulkData.Lock(LOCK_READ_ONLY);

        for (int32 i = 0; i < TextureSize * TextureSize; i++)
        {
            Data[i] = AttrData[i].R / 255.0f;
        }

        AttrMip->BulkData.Unlock();
    }

    // Calculate viability for each pixel
    for (int32 Y = 0; Y < TextureSize; Y++)
    {
        for (int32 X = 0; X < TextureSize; X++)
        {
            int32 Index = Y * TextureSize + X;

            // Build attributes map for this pixel
            TMap<FString, float> PixelAttributes;
            for (const auto& Pair : AttributeData)
            {
                PixelAttributes.Add(Pair.Key, Pair.Value[Index]);
            }

            // Calculate world position for noise
            float U = (float)X / (TextureSize - 1);
            float V = (float)Y / (TextureSize - 1);
            FVector2D WorldPos2D(
                FMath::Lerp(-CachedLandscapeExtent.X, CachedLandscapeExtent.X, U),
                FMath::Lerp(-CachedLandscapeExtent.Y, CachedLandscapeExtent.Y, V)
            );

            // Calculate viability
            float Viability = CalculateViabilityFromAttributes(Species, PixelAttributes, WorldPos2D);

            // Convert to color
            FLinearColor Color = GetViabilityColor(Viability);
            ViabilityData[Index] = Color.ToFColor(true);
        }
    }

    // Unlock and update
    Mip0->BulkData.Unlock();
    ViabilityPreviewTexture->UpdateResource();

    // Update preview widget
    ViabilityPreviewWidget->SetPreviewTexture(ViabilityPreviewTexture);
}

float FBiomeDataEditor::CalculateHeightAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    FVector Origin = Location + FVector(0, 0, 10000.0f);
    FVector End = Location - FVector(0, 0, 10000.0f);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;

    if (Landscape->GetWorld()->LineTraceSingleByChannel(HitResult, Origin, End, ECC_Visibility, QueryParams))
    {
        if (Cast<ALandscapeProxy>(HitResult.GetActor()))
        {
            // Normalize height to 0-1 range (adjust based on your landscape)
            float Height = HitResult.Location.Z;
            return FMath::Clamp((Height + 1000.0f) / 4000.0f, 0.0f, 1.0f);
        }
    }

    return 0.5f;
}

float FBiomeDataEditor::CalculateSlopeAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    const float SampleOffset = 50.0f;

    float HeightCenter = CalculateHeightAtLocation(Location, Landscape);
    float HeightRight = CalculateHeightAtLocation(Location + FVector(SampleOffset, 0, 0), Landscape);
    float HeightForward = CalculateHeightAtLocation(Location + FVector(0, SampleOffset, 0), Landscape);

    FVector TangentX = FVector(SampleOffset, 0, (HeightRight - HeightCenter) * 4000.0f);
    FVector TangentY = FVector(0, SampleOffset, (HeightForward - HeightCenter) * 4000.0f);

    FVector Normal = FVector::CrossProduct(TangentY, TangentX).GetSafeNormal();
    float SlopeDegrees = FMath::RadiansToDegrees(FMath::Acos(Normal.Z));

    return FMath::Clamp(SlopeDegrees / 90.0f, 0.0f, 1.0f);
}

float FBiomeDataEditor::CalculateAspectAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    const float SampleOffset = 50.0f;

    float HeightCenter = CalculateHeightAtLocation(Location, Landscape);
    float HeightRight = CalculateHeightAtLocation(Location + FVector(SampleOffset, 0, 0), Landscape);
    float HeightForward = CalculateHeightAtLocation(Location + FVector(0, SampleOffset, 0), Landscape);
    float HeightLeft = CalculateHeightAtLocation(Location + FVector(-SampleOffset, 0, 0), Landscape);
    float HeightBack = CalculateHeightAtLocation(Location + FVector(0, -SampleOffset, 0), Landscape);

    float Dx = (HeightRight - HeightLeft) / (2.0f * SampleOffset);
    float Dy = (HeightForward - HeightBack) / (2.0f * SampleOffset);

    FVector2D Gradient(-Dx, -Dy);
    if (Gradient.IsNearlyZero())
    {
        return 0.0f;
    }

    Gradient.Normalize();
    float Angle = FMath::Atan2(Gradient.X, Gradient.Y);
    if (Angle < 0) Angle += 2.0f * PI;

    return Angle / (2.0f * PI);
}

float FBiomeDataEditor::CalculateCurvatureAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.5f;

    const float SampleDistance = 50.0f;

    float HeightCenter = CalculateHeightAtLocation(Location, Landscape);
    float HeightNorth = CalculateHeightAtLocation(Location + FVector(0, SampleDistance, 0), Landscape);
    float HeightSouth = CalculateHeightAtLocation(Location + FVector(0, -SampleDistance, 0), Landscape);
    float HeightEast = CalculateHeightAtLocation(Location + FVector(SampleDistance, 0, 0), Landscape);
    float HeightWest = CalculateHeightAtLocation(Location + FVector(-SampleDistance, 0, 0), Landscape);

    float D2x = (HeightEast - 2 * HeightCenter + HeightWest) / (SampleDistance * SampleDistance);
    float D2y = (HeightNorth - 2 * HeightCenter + HeightSouth) / (SampleDistance * SampleDistance);

    float Curvature = (D2x + D2y) * 0.5f;
    return FMath::Clamp(Curvature * 50.0f + 0.5f, 0.0f, 1.0f);
}

float FBiomeDataEditor::CalculateOcclusionAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    const float Radius = 500.0f;
    const int32 Samples = 16;
    const float AngleBias = 5.0f;

    float TotalOcclusion = 0.0f;
    const float AngleStep = 2.0f * PI / Samples;
    const float BiasRadians = FMath::DegreesToRadians(AngleBias);

    FVector BaseLocation = Location;
    BaseLocation.Z = CalculateHeightAtLocation(Location, Landscape) * 4000.0f - 1000.0f;

    for (int32 i = 0; i < Samples; ++i)
    {
        float Angle = i * AngleStep;
        FVector Direction(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f);

        float MaxElevation = -BiasRadians;
        for (float Distance = Radius * 0.1f; Distance <= Radius; Distance += Radius * 0.1f)
        {
            FVector SampleLocation = BaseLocation + Direction * Distance;
            float SampleHeight = CalculateHeightAtLocation(SampleLocation, Landscape) * 4000.0f - 1000.0f;

            float ElevationAngle = FMath::Atan2(SampleHeight - BaseLocation.Z, Distance);
            MaxElevation = FMath::Max(MaxElevation, ElevationAngle);
        }

        TotalOcclusion += FMath::Clamp(MaxElevation / (PI * 0.5f) + 0.5f, 0.0f, 1.0f);
    }

    return 1.0f - (TotalOcclusion / Samples);
}

float FBiomeDataEditor::CalculateFlowAccumulationAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    const float CellSize = 100.0f;
    float AccumulatedFlow = 0.0f;

    const FVector2D Directions[8] = {
        FVector2D(0, 1), FVector2D(1, 1), FVector2D(1, 0), FVector2D(1, -1),
        FVector2D(0, -1), FVector2D(-1, -1), FVector2D(-1, 0), FVector2D(-1, 1)
    };

    float CurrentHeight = CalculateHeightAtLocation(Location, Landscape);

    for (int32 iter = 0; iter < 3; ++iter)
    {
        float IterationRadius = CellSize * FMath::Pow(2.0f, iter);

        for (int32 dir = 0; dir < 8; ++dir)
        {
            FVector SampleLocation = Location + FVector(Directions[dir].X, Directions[dir].Y, 0) * IterationRadius;
            float SampleHeight = CalculateHeightAtLocation(SampleLocation, Landscape);

            if (SampleHeight > CurrentHeight + 0.01f)
            {
                float Weight = (dir % 2 == 0) ? 1.0f : 0.707f;
                AccumulatedFlow += Weight * (SampleHeight - CurrentHeight) / IterationRadius;
            }
        }
    }

    return FMath::Clamp(AccumulatedFlow / 3.0f, 0.0f, 1.0f);
}

float FBiomeDataEditor::CalculateWindExposureAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape, const FVector& WindDir) const
{
    if (!Landscape) return 1.0f;

    const float Radius = 1000.0f;
    const int32 Samples = 10;
    float TotalExposure = 0.0f;

    float BaseHeight = CalculateHeightAtLocation(Location, Landscape);

    for (int32 i = 1; i <= Samples; ++i)
    {
        float Distance = (Radius * i) / Samples;
        FVector SampleLocation = Location - WindDir * Distance;
        float SampleHeight = CalculateHeightAtLocation(SampleLocation, Landscape);

        float HeightDifference = BaseHeight - SampleHeight;
        float Angle = FMath::Atan2(HeightDifference, Distance / 4000.0f);

        float Exposure = FMath::Clamp(1.0f + (Angle / (PI * 0.25f)), 0.0f, 1.0f);
        TotalExposure += Exposure;
    }

    return TotalExposure / Samples;
}

float FBiomeDataEditor::CalculateViabilityFromAttributes(const FBiomeSpecies& Species, const TMap<FString, float>& Attributes, const FVector2D& WorldPos) const
{
    if (Species.ViabilityDataGroups.Num() == 0)
    {
        return 0.0f;
    }

    float TotalViability = 0.0f;

    // Process each data group
    for (const FViabilityDataGroup& DataGroup : Species.ViabilityDataGroups)
    {
        if (DataGroup.Attributes.Num() == 0)
        {
            continue;
        }

        float GroupViability = 1.0f; // Start at 1 for multiplication

        // Process each attribute in the group (multiply together)
        for (const FViabilityAttribute& ViabilityAttr : DataGroup.Attributes)
        {
            float AttributeValue = 0.0f;

            // Get attribute value
            FString AttributeName;
            switch (ViabilityAttr.Attribute)
            {
            case ETerrainAttribute::Altitude: AttributeName = TEXT("Height"); break;
            case ETerrainAttribute::Slope: AttributeName = TEXT("Slope"); break;
            case ETerrainAttribute::Aspect: AttributeName = TEXT("Aspect"); break;
            case ETerrainAttribute::Curvature: AttributeName = TEXT("Curvature"); break;
            case ETerrainAttribute::Occlusion: AttributeName = TEXT("Occlusion"); break;
            case ETerrainAttribute::FlowAccumulation: AttributeName = TEXT("FlowAccumulation"); break;
            case ETerrainAttribute::WindExposure: AttributeName = TEXT("WindExposure"); break;
            default: continue;
            }

            if (const float* ValuePtr = Attributes.Find(AttributeName))
            {
                AttributeValue = *ValuePtr;
            }

            // Apply power
            float ProcessedValue = AttributeValue;
            if (FMath::Abs(ViabilityAttr.Power) > SMALL_NUMBER)
            {
                if (ViabilityAttr.Power < 0)
                {
                    // Negative power inverts the value first
                    ProcessedValue = 1.0f - ProcessedValue;
                    ProcessedValue = FMath::Pow(ProcessedValue, FMath::Abs(ViabilityAttr.Power));
                }
                else
                {
                    ProcessedValue = FMath::Pow(ProcessedValue, ViabilityAttr.Power);
                }
            }

            // Apply ramp/curve if available
            if (ViabilityAttr.Ramp)
            {
                ProcessedValue = ViabilityAttr.Ramp->GetFloatValue(ProcessedValue);
            }

            // Multiply into group viability
            GroupViability *= ProcessedValue;
        }

        // Apply noise if enabled
        if (DataGroup.Noise.bEnabled)
        {
            FVector2D NoisePos = (WorldPos + DataGroup.Noise.Offset) / DataGroup.Noise.Scale;
            float NoiseValue = FMath::PerlinNoise2D(NoisePos);
            NoiseValue = (NoiseValue + 1.0f) * 0.5f; // Remap from [-1,1] to [0,1]
            NoiseValue = FMath::Lerp(1.0f - DataGroup.Noise.Amplitude, 1.0f, NoiseValue);
            GroupViability *= NoiseValue;
        }

        // Apply group weight and add to total
        TotalViability += GroupViability * DataGroup.GroupWeight;
    }

    // Clamp final result
    return FMath::Clamp(TotalViability, 0.0f, 1.0f);
}

FLinearColor FBiomeDataEditor::GetViabilityColor(float Viability) const
{
    // Color gradient: Black -> Red -> Yellow -> Green
    FLinearColor Color;

    if (Viability < 0.33f)
    {
        // Black to Red
        float T = Viability / 0.33f;
        Color = FLinearColor::LerpUsingHSV(FLinearColor::Black, FLinearColor::Red, T);
    }
    else if (Viability < 0.66f)
    {
        // Red to Yellow
        float T = (Viability - 0.33f) / 0.33f;
        Color = FLinearColor::LerpUsingHSV(FLinearColor::Red, FLinearColor::Yellow, T);
    }
    else
    {
        // Yellow to Green
        float T = (Viability - 0.66f) / 0.34f;
        Color = FLinearColor::LerpUsingHSV(FLinearColor::Yellow, FLinearColor::Green, T);
    }

    // Add some transparency for low viability
    Color.A = FMath::Max(0.3f, Viability);

    return Color;
}

// Details Customization Implementation
TSharedRef<IDetailCustomization> FBiomeDataDetailsCustomization::MakeInstance()
{
    return MakeShareable(new FBiomeDataDetailsCustomization());
}

void FBiomeDataDetailsCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // get the objects being customized
    TArray<TWeakObjectPtr<UObject>> Objects;
    DetailBuilder.GetObjectsBeingCustomized(Objects);
    if (Objects.Num() == 0) return;

    UBiomeData* EditedBiomeData = Cast<UBiomeData>(Objects[0].Get());
    if (!EditedBiomeData) return;

    // Now get the array handle, count elements, etc:
    TSharedPtr<IPropertyHandle> SpeciesProperty = DetailBuilder.GetProperty(GET_MEMBER_NAME_CHECKED(UBiomeData, Species));
    if (!SpeciesProperty.IsValid()) return;

    TSharedPtr<IPropertyHandleArray> SpeciesArray = SpeciesProperty->AsArray();
    if (!SpeciesArray.IsValid()) return;

    uint32 NumSpecies = 0;
    SpeciesArray->GetNumElements(NumSpecies);

    IDetailCategoryBuilder& SpeciesCategory = DetailBuilder.EditCategory("Species");

    for (uint32 Index = 0; Index < NumSpecies; ++Index)
    {
        // Read the species name
        TSharedRef<IPropertyHandle> ElemHandle = SpeciesArray->GetElement(Index);
        TSharedPtr<IPropertyHandle> NameHandle = ElemHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FBiomeSpecies, SpeciesName));

        FString SpeciesName = TEXT("?");
        if (NameHandle.IsValid())
        {
            NameHandle->GetValue(SpeciesName);
        }

        // Add a button row
        SpeciesCategory.AddCustomRow(FText::FromString("Preview"))
            .ValueContent()
            [
                SNew(SButton)
                    .Text(FText::Format(FText::FromString("Preview {0} Viability"), FText::FromString(SpeciesName)))
                    .OnClicked_Lambda([EditedBiomeData, Index]()->FReply
                         {
                            if (TSharedPtr<IToolkit> Toolkit = FToolkitManager::Get().FindEditorForAsset(EditedBiomeData))
                            {
                                if (TSharedPtr<FBiomeDataEditor> Editor = StaticCastSharedPtr<FBiomeDataEditor>(Toolkit))
                                {
                                    Editor->PreviewSpeciesViability(Index);
                                }
                            }
                     return FReply::Handled();
                    })
            ];
    }
}



// Viability Preview Widget Implementation
void SBiomeViabilityPreviewWidget::Construct(const FArguments& InArgs, FBiomeDataEditor* InEditor)
{
    BiomeEditor = InEditor;

    RefreshLandscapeList();

    ChildSlot
        [
            SNew(SVerticalBox)
                + SVerticalBox::Slot()
                .FillHeight(1.0f)
                .Padding(5)
                [
                    SNew(SBox)
                        .WidthOverride(512)
                        .HeightOverride(512)
                        [
                            SAssignNew(PreviewImage, SImage)
                                .Image(FEditorStyle::GetBrush("WhiteBrush"))
                        ]
                ]
                + SVerticalBox::Slot()
                .AutoHeight()
                .Padding(5)
                [
                    SNew(SHorizontalBox)
                        + SHorizontalBox::Slot()
                        .FillWidth(1.0f)
                        .Padding(2)
                        [
                            SAssignNew(LandscapeComboBox, SComboBox<TWeakObjectPtr<ALandscapeStreamingProxy>>)
                                .OptionsSource(&AvailableLandscapes)
                                .OnSelectionChanged(this, &SBiomeViabilityPreviewWidget::OnLandscapeSelected)
                                .OnGenerateWidget_Lambda([](TWeakObjectPtr<ALandscapeStreamingProxy> InProxy)
                                    {
                                        FString Name = InProxy.IsValid() ? InProxy.Get()->GetName() : TEXT("Invalid");
                                        return SNew(STextBlock).Text(FText::FromString(Name));
                                    })
                                .Content()
                                [
                                    SNew(STextBlock)
                                        .Text(this, &SBiomeViabilityPreviewWidget::GetSelectedLandscapeText)
                                ]
                        ]
                        + SHorizontalBox::Slot()
                        .AutoWidth()
                        .Padding(2)
                        [
                            SNew(SButton)
                                .Text(FText::FromString("Bake Terrain Data"))
                                .OnClicked(this, &SBiomeViabilityPreviewWidget::OnBakeTerrainClicked)
                        ]
                ]
                + SVerticalBox::Slot()
                .AutoHeight()
                .Padding(5)
                [
                    SAssignNew(InfoText, STextBlock)
                        .Text(FText::FromString("Select a landscape proxy and bake terrain data to preview species viability."))
                ]
        ];
}

void SBiomeViabilityPreviewWidget::SetPreviewTexture(UTexture2D* InTexture)
{
    if (InTexture && PreviewImage.IsValid())
    {
        FSlateBrush* Brush = new FSlateBrush();
        Brush->SetResourceObject(InTexture);
        Brush->ImageSize = FVector2D(512, 512);
        PreviewImage->SetImage(Brush);
    }
}

void SBiomeViabilityPreviewWidget::RefreshLandscapeList()
{
    AvailableLandscapes.Empty();

    if (BiomeEditor != nullptr)
    {
        UBiomeData* DataAsset = BiomeEditor->GetBiomeData();
        if (DataAsset)
        {
            UWorld* World = DataAsset->GetWorld();
            if (World)
            {
                for (TActorIterator<ALandscapeStreamingProxy> It(World); It; ++It)
                {
                    ALandscapeStreamingProxy* Proxy = *It;
                    if (Proxy)
                    {
                        // Create weak pointer and add to array
                        AvailableLandscapes.Add(Proxy);
                    }
                }
            }
        }
    }

    if (LandscapeComboBox.IsValid())
    {
        LandscapeComboBox->RefreshOptions();
    }
}

FReply SBiomeViabilityPreviewWidget::OnBakeTerrainClicked()
{
    if (BiomeEditor != nullptr)
    {
        BiomeEditor->BakeTerrainData(); // Direct call
    }
    return FReply::Handled();
}

void SBiomeViabilityPreviewWidget::OnLandscapeSelected(TWeakObjectPtr<ALandscapeStreamingProxy> InProxy, ESelectInfo::Type SelectInfo)
{
    if (BiomeEditor != nullptr)
    {
        ALandscapeStreamingProxy* ProxyPtr = InProxy.Get(); // Get the raw pointer from TWeakObjectPtr
        BiomeEditor->SetSelectedLandscapeProxy(ProxyPtr); // Direct call

        if (InfoText.IsValid()) // Check STextBlock
        {
            if (ProxyPtr)
            {
                InfoText->SetText(FText::Format(
                    FText::FromString("Selected: {0}. Click 'Bake Terrain Data' to analyze terrain attributes."),
                    FText::FromString(ProxyPtr->GetName())
                ));
            }
            else
            {
                InfoText->SetText(FText::FromString("Selected landscape is no longer valid."));
            }
        }
    }
}

FText SBiomeViabilityPreviewWidget::GetSelectedLandscapeText() const
{
    if (BiomeEditor != nullptr)
    {
        // GetSelectedLandscapeProxy() returns ALandscapeStreamingProxy*
        ALandscapeStreamingProxy* Selected = BiomeEditor->GetSelectedLandscapeProxy();
        if (Selected)
        {
            return FText::FromString(Selected->GetName());
        }
    }
    return FText::FromString("Select Landscape Proxy...");
}